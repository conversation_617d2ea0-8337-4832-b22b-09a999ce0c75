package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_PRAF_Router(router *gin.RouterGroup) {
	router.GET("/get-aex-praf", controllers.AEX_PRAF.GetAEX_PRAF)
	router.POST("/aex-praf", controllers.AEX_PRAF.InsertAEX_PRAF)
	router.PUT("/aex-praf", controllers.AEX_PRAF.UpdateAEX_PRAF)
	router.DELETE("/aex-praf", controllers.AEX_PRAF.DeleteAEX_PRAF)

	router.GET("/get-material-library", controllers.AEX_PRAF.GetMaterialLibrary)
	router.GET("/get-all-material-library", controllers.AEX_PRAF.GetALLMaterialLibrary)
	
}
