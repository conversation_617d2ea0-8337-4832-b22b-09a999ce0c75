package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_FMEAServices struct {
	*BaseService
}

var AEX_FMEA = &AEX_FMEAServices{}

func (s *AEX_FMEAServices) GetAEX_FMEA() ([]types.AEXFMEAReportForm, error) {
	var res []types.AEXFMEAReportForm
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()

	query := `
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			Stage,
			Skus,
			Category,
			Subcategory,
			IlusTfmode,
			FuncReq,
			PotentialFailureMode,
			PotentialEffect,
			PotentialCause,
			Prevention,
			Detection,
			RecommendedAction,
			Responsibility,
			TargetCompletioDate,
			ActionsTaken,
			CLWID,
			ProjectLead,
			ProjectName,
			Season,
			CoreTeam,
			UserID,
			UserDate,
			CreateDate
		FROM AEX_FMEA_ReportForm
		Order by CreateDate DESC
	`
	err = db.Raw(query).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()
	return res, nil
}

func (s *AEX_FMEAServices) InsertAEX_FMEA(req *types.AEXFMEAReportForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO AEX_FMEA_ReportForm
		(
			ID,
			Stage,
			Skus,
			Category,
			Subcategory,
			IlusTfmode,
			FuncReq,
			PotentialFailureMode,
			PotentialEffect,
			PotentialCause,
			Prevention,
			Detection,
			RecommendedAction,
			Responsibility,
			TargetCompletioDate,
			ActionsTaken,
			CLWID,
			ProjectLead,
			ProjectName,
			Season,
			CoreTeam,
			UserID,
			UserDate,
			CreateDate
		)
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		VALUES (
			NEWID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GetDate(), GetDate()
		);
	`
	if err := tx.Raw(
		query,
		req.Stage,
		req.Skus,
		req.Category,
		req.Subcategory,
		req.IlusTfmode,
		req.FuncReq,
		req.PotentialFailureMode,
		req.PotentialEffect,
		req.PotentialCause,
		req.Prevention,
		req.Detection,
		req.RecommendedAction,
		req.Responsibility,
		req.TargetCompletioDate,
		req.ActionsTaken,
		req.CLWID,
		req.ProjectLead,
		req.ProjectName,
		req.Season,
		req.CoreTeam,
		req.UserID,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

func (s *AEX_FMEAServices) UpdateAEX_FMEA(req *types.AEXFMEAReportForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var ID string

	query := `
		UPDATE AEX_FMEA_ReportForm
		SET 
			Stage = ?,
			Skus = ?,
			Category = ?,
			Subcategory = ?,
			IlusTfmode = ?,
			FuncReq = ?,
			PotentialFailureMode = ?,
			PotentialEffect = ?,
			PotentialCause = ?,
			Prevention = ?,
			Detection = ?,
			RecommendedAction = ?,
			Responsibility = ?,
			TargetCompletioDate = ?,
			ActionsTaken = ?,
			CLWID = ?,
			ProjectLead = ?,
			ProjectName = ?,
			Season = ?,
			CoreTeam = ?,
			UserID = ?,
			UserDate = GetDate()
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.Stage,
		req.Skus,
		req.Category,
		req.Subcategory,
		req.IlusTfmode,
		req.FuncReq,
		req.PotentialFailureMode,
		req.PotentialEffect,
		req.PotentialCause,
		req.Prevention,
		req.Detection,
		req.RecommendedAction,
		req.Responsibility,
		req.TargetCompletioDate,
		req.ActionsTaken,
		req.CLWID,
		req.ProjectLead,
		req.ProjectName,
		req.Season,
		req.CoreTeam,
		req.UserID,
		req.ID,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

func (s *AEX_FMEAServices) DeleteAEX_FMEA(req *types.AEXFMEAReportForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var deletedID string

	query := `
		DELETE FROM AEX_FMEA_ReportForm
		OUTPUT CAST(DELETED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.ID,
	).Scan(
		&deletedID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return deletedID, nil
}
