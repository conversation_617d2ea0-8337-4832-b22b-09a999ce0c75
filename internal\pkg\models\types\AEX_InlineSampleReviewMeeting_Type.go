package types

type AEX_InlineSampleReviewMeeting struct {
	ID                    string `gorm:"column:ID"`
	ColorwayID            string `gorm:"column:ColorwayID"`
	Category              string `gorm:"column:Category"`
	SubProductLine        string `gorm:"column:SubProductLine"`
	ProductDescription    string `gorm:"column:ProductDescription"`
	Cut                   string `gorm:"column:Cut"`
	GenderClass           string `gorm:"column:GenderClass"`
	SampleSize            string `gorm:"column:SampleSize"`
	TechLevel             string `gorm:"column:TechLevel"`
	ProductDeveloper      string `gorm:"column:ProductDeveloper"`
	CLO                   string `gorm:"column:CLO"`
	UserDate              string `gorm:"column:UserDate"`
	UserID                string `gorm:"column:UserID"`
	CreateDate            string `gorm:"column:CreateDate"`
	Season                string `gorm:"column:Season"`
	Stage                 string `gorm:"column:Stage"`
	IllustrateImg         string `gorm:"column:IllustrateImg"`
	Material              string `gorm:"column:Material"`
	CTS_Factory_Comments  string `gorm:"column:CTS_Factory_Comments"`
	FactoryCostSuggestion string `gorm:"column:FactoryCostSuggestion"`
	HQComment             string `gorm:"column:HQComment"`
	ProductForecast       string `gorm:"column:ProductForecast"`
	TargetSamplePrice     string `gorm:"column:TargetSamplePrice"`

	MaterialInline    string `gorm:"column:MaterialInline"`
	MoldCharge        string `gorm:"column:MoldCharge"`
	BaseFactory       string `gorm:"column:BaseFactory"`
	FOBDoller         string `gorm:"column:FOBDoller"`
	FOBPercent        string `gorm:"column:FOBPercent"`
	CTSCostingComment string `gorm:"column:CTSCostingComment"`
}
