package services

import (
	"fmt"
	"os"
	"time"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/types"

	"github.com/dgrijalva/jwt-go"
)

// FILE - ADMIN SERVICE
type AdminService struct {
	*BaseService
}

var Admin = &AdminService{}

// func - LoginService

func (s *AdminService) LoginService(requestParams *request.LoginRequest) (types.Admin, error) {
	var admin types.Admin

	// Kết nối cơ sở dữ liệu
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return types.Admin{}, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn lấy thông tin admin
	query := `	select BUsers.USERID as UserID,USERNAME as UserN<PERSON>,PWD as Password,ISNULL(AEX_Role.Role,0) as Role from BUser<PERSON>
				left join AEX_Role on AEX_Role.UserID = Busers.USERID
				where BUsers.USERID = ? `
	err = db.Raw(query, requestParams.UserID).Scan(&admin).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return types.Admin{}, err
	}

	// So sánh mật khẩu
	if admin.Password != requestParams.Password {
		return types.Admin{}, nil
	}

	// Tạo JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"UserID":   admin.UserID,
		"Password": admin.Password,
		"Time":     time.Now().Add(time.Hour * 24).Unix(), // Token hết hạn sau 24 giờ
	})

	// Ký token
	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_KEY")))
	if err != nil {
		fmt.Println("Token signing error:", err)
		return types.Admin{}, err
	}
	// fmt.Println("Token:", tokenString)
	admin.Token = tokenString
	admin.Password = ""
	return admin, nil
}

// func - UpdatePasswordService
func (s *AdminService) UpdatePasswordService(requestParams *request.UpdatePasswordRequest) (string, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		return "error", fmt.Errorf("database connection error: %v", err)
	}
	dbInstance, _ := db.DB()
	query := `
	UPDATE Busers 
	SET PWD = ?
	WHERE USERID =?
	`
	err = db.Exec(query, requestParams.Password, requestParams.Adminid).Error
	if err != nil {
		return "error", err
	}
	dbInstance.Close()
	return "success", nil
}
