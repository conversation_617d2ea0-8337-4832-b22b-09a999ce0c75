package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_InlineSampleReviewMeetingController struct {
	*BaseController
}

var AEX_InlineSampleReviewMeeting = &AEX_InlineSampleReviewMeetingController{}

func (c *AEX_InlineSampleReviewMeetingController) GetAllInlineSampleReviewMeeting(ctx *gin.Context) {
	result, err := services.AEX_InlineSampleReviewMeeting_Service.GetAllInlineSampleReviewMeeting()
	if err != nil {
		log.Printf("Error fetching InlineSampleReviewMeeting: %v", err)
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *AEX_InlineSampleReviewMeetingController) InsertAEX_InlineSampleReviewMeeting(ctx *gin.Context) {
	var requestParams *types.AEX_InlineSampleReviewMeeting
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_InlineSampleReviewMeeting_Service.InsertAEX_InlineSampleReviewMeeting(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Sample Review Meeting`,`Insert`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}
func (c *AEX_InlineSampleReviewMeetingController) UpdateAEX_InlineSampleReviewMeeting(ctx *gin.Context) {
	var requestParams *types.AEX_InlineSampleReviewMeeting
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_InlineSampleReviewMeeting_Service.UpdateAEX_InlineSampleReviewMeeting(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Sample Review Meeting`,`Update`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}

func (c *AEX_InlineSampleReviewMeetingController) DeleteAEX_InlineSampleReviewMeeting(ctx *gin.Context) {
	var requestParams *types.AEX_InlineSampleReviewMeeting
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_InlineSampleReviewMeeting_Service.DeleteAEX_InlineSampleReviewMeeting(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Sample Review Meeting`,`Delete`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}
