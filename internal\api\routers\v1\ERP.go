package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterERP(router *gin.RouterGroup) {
	router.POST("/get-link-image", controllers.ERP.GetLinkImage)
	router.GET("/get-image", controllers.ERP.LoadingImage)

	router.POST("/get-modle", controllers.ERP.GetModelColorwayID)

	router.GET("/get-cts", controllers.ERP.GetCTS)         // CLODev
	router.GET("/get-devcode", controllers.ERP.GetDEVCODE) // ColorwayID
	router.GET("/get-fd", controllers.ERP.GetFD)           //FTYDev    // Project Lead:
	router.GET("/get-jijie", controllers.ERP.GetJiJie)     // Season
	router.GET("/get-kflx", controllers.ERP.GetKFLX)       //TechLvl
	router.GET("/get-na", controllers.ERP.GetNA)           // HQDev
	router.GET("/get-xieming", controllers.ERP.GetXieMing) // Modole Name , Product Description, Project Name
	router.GET("/get-acticle", controllers.ERP.GetARTICLE)
	router.GET("/get-fd2", controllers.ERP.GetFD2)   //LYN DEV
	router.GET("/get-xtmh", controllers.ERP.GetXTMH) //Last
	router.GET("/get-ddmh", controllers.ERP.GetDDMH) //Outsole
	router.GET("/get-category", controllers.ERP.GetCategory)
	router.GET("/get-cut", controllers.ERP.GetCut)                  // Modole Name , Product Description
	router.GET("/get-samplesize", controllers.ERP.GetSampleSize)    // Modole Name , Product Description
	router.GET("/get-season", controllers.ERP.GetSEASONMeetingForm) // Season from form Meeting

	router.GET("/get-acticle-devcode", controllers.ERP.GetARTICLE_byDevCode)

}
