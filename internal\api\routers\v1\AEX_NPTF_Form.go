package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_NPTF_Router(router *gin.RouterGroup) {
	router.GET("/get-aex-nptf", controllers.AEX_NPTF.GetAEX_NPTF)
	router.POST("/aex-nptf", controllers.AEX_NPTF.InsertAEX_NPTF)
	router.PUT("/aex-nptf", controllers.AEX_NPTF.UpdateAEX_NPTF)
	router.DELETE("/aex-nptf", controllers.AEX_NPTF.DeleteAEX_NPTF)
	
}
