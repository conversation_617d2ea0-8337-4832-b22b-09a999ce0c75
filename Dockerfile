FROM golang:1.22-alpine AS build

RUN apk add --no-cache git gcc g++ musl-dev

WORKDIR /src

ENV CGO_ENABLED=0
ENV GO111MODULE=on

COPY go.mod .
COPY go.sum .
RUN go mod download

COPY . .
COPY .env.production .env.production
COPY mount_smb.sh /src/mount_smb.sh

RUN go build -o ./out/webapi ./cmd/main.go

# Final image
FROM alpine:3.17.0

RUN apk add --no-cache ca-certificates cifs-utils bash

WORKDIR /app

COPY --from=build /src/log /app/log
COPY --from=build /src/out/webapi /app/webapi
COPY --from=build /src/data /app/data
COPY --from=build /src/.env.production /app/.env.production
COPY --from=build /src/mount_smb.sh /app/mount_smb.sh
COPY entrypoint.sh /app/entrypoint.sh

RUN chmod +x /app/webapi /app/mount_smb.sh /app/entrypoint.sh

EXPOSE 8008

ENTRYPOINT ["/app/entrypoint.sh"]