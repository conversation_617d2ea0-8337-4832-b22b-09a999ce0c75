package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_InlineSampleReviewMeeting struct {
	*BaseService
}

var AEX_InlineSampleReviewMeeting_Service = &AEX_InlineSampleReviewMeeting{}

func (s *AEX_InlineSampleReviewMeeting) GetAllInlineSampleReviewMeeting() ([]types.AEX_InlineSampleReviewMeeting, error) {
	var arrInlineSampleReviewMeeting []types.AEX_InlineSampleReviewMeeting
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	query := `
	SELECT 
		CAST(ID AS NVARCHAR(36)) AS ID,
		ColorwayID,
		Category,
		SubProductLine,
		ProductDescription,
		Cut,
		GenderClass,
		SampleSize,
		TechLevel,
		ProductDeveloper,
		CLO,
		UserDate,
		UserID,
		CreateDate,
		Season,
		Stage,
		IllustrateImg,
		Material,
		CTS_Factory_Comments,
		FactoryCostSuggestion,
		HQComment,
		ProductForecast,
		TargetSamplePrice,
		MaterialInline,
		MoldCharge,
		BaseFactory,
		FOBDoller,
		FOBPercent,
		CTSCostingComment
	FROM AEX_SR_Meeting_Form
	ORDER BY CreateDate DESC
	`
	err = db.Raw(query).Scan(&arrInlineSampleReviewMeeting).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()

	return arrInlineSampleReviewMeeting, nil
}
func (s *AEX_InlineSampleReviewMeeting) InsertAEX_InlineSampleReviewMeeting(req *types.AEX_InlineSampleReviewMeeting) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}

	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var ID string

	query := `
		INSERT INTO AEX_SR_Meeting_Form (
			ID,
			ColorwayID,
			Category,
			SubProductLine,
			ProductDescription,
			Cut,
			GenderClass,
			SampleSize,
			TechLevel,
			ProductDeveloper,
			CLO,
			UserDate,
			UserID,
			CreateDate,
			Season,
			Stage,
			IllustrateImg,
			Material,
			CTS_Factory_Comments,
			FactoryCostSuggestion,
			HQComment,
			ProductForecast,
			TargetSamplePrice,
			MaterialInline,
			MoldCharge,
			BaseFactory,
			FOBDoller,
			FOBPercent,
			CTSCostingComment

		)
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		VALUES (
			NEWID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), ?, GETDATE(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,? ,? ,? ,? ,?
		)
	`
	if err := tx.Raw(
		query,
		req.ColorwayID,
		req.Category,
		req.SubProductLine,
		req.ProductDescription,
		req.Cut,
		req.GenderClass,
		req.SampleSize,
		req.TechLevel,
		req.ProductDeveloper,
		req.CLO,
		req.UserID,
		req.Season,
		req.Stage,
		req.IllustrateImg,
		req.Material,
		req.CTS_Factory_Comments,
		req.FactoryCostSuggestion,
		req.HQComment,
		req.ProductForecast,
		req.TargetSamplePrice,
		req.MaterialInline,
		req.MoldCharge,
		req.BaseFactory,
		req.FOBDoller,
		req.FOBPercent,
		req.CTSCostingComment,
	).Scan(&ID).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return ID, nil
}
func (s *AEX_InlineSampleReviewMeeting) UpdateAEX_InlineSampleReviewMeeting(req *types.AEX_InlineSampleReviewMeeting) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}

	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var ID string

	query := `
		UPDATE AEX_SR_Meeting_Form
		SET
			ColorwayID = ?,
			Category = ?,
			SubProductLine = ?,
			ProductDescription = ?,
			Cut = ?,
			GenderClass = ?,
			SampleSize = ?,
			TechLevel = ?,
			ProductDeveloper = ?,
			CLO = ?,
			UserDate = GetDate(),
			UserID = ?,
			Season = ?,
			Stage = ?,
			IllustrateImg = ?,
			Material = ?,
			CTS_Factory_Comments = ?,
			FactoryCostSuggestion = ?,
			HQComment = ?,
			ProductForecast = ?,
			TargetSamplePrice = ?,
			MaterialInline = ?,
			MoldCharge = ?,
			BaseFactory = ?,
			FOBDoller = ?,
			FOBPercent = ?,
			CTSCostingComment = ?
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?
	`

	if err := tx.Raw(
		query,
		req.ColorwayID,
		req.Category,
		req.SubProductLine,
		req.ProductDescription,
		req.Cut,
		req.GenderClass,
		req.SampleSize,
		req.TechLevel,
		req.ProductDeveloper,
		req.CLO,
		req.UserID,
		req.Season,
		req.Stage,
		req.IllustrateImg,
		req.Material,
		req.CTS_Factory_Comments,
		req.FactoryCostSuggestion,
		req.HQComment,
		req.ProductForecast,
		req.TargetSamplePrice,
		req.MaterialInline,
		req.MoldCharge,
		req.BaseFactory,
		req.FOBDoller,
		req.FOBPercent,
		req.CTSCostingComment,
		req.ID,
	).Scan(&ID).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute update: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return ID, nil
}
func (s *AEX_InlineSampleReviewMeeting) DeleteAEX_InlineSampleReviewMeeting(req *types.AEX_InlineSampleReviewMeeting) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var updatedID string

	query := `
		DELETE FROM AEX_SR_Meeting_Form
		OUTPUT CAST(DELETED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.ID,
	).Scan(
		&updatedID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return updatedID, nil
}
