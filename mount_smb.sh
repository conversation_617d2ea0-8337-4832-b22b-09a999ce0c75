#!/bin/bash
set -e

SMB_SERVER="192.168.123.113"
SMB_SHARE="pfc/Development_Page"
SMB_USERNAME="uof"
SMB_PASSWORD="Tyxuan@123"
MOUNT_POINT="/mnt/Development_Page"

SMB_SERVER_1="192.168.123.111"
SMB_SHARE_1="Develop Team"
SMB_USERNAME_1="erppic"
SMB_PASSWORD_1="Tyxuan@2024"
MOUNT_POINT_1="/mnt/123_111"

mkdir -p "$MOUNT_POINT"
mkdir -p "$MOUNT_POINT_1"

echo "✅ Đã load biến môi trường, bắt đầu mount..."

echo "[INFO] Đang mount //${SMB_SERVER}/${SMB_SHARE} -> ${MOUNT_POINT}..."
mount -t cifs "//${SMB_SERVER}/${SMB_SHARE}" "$MOUNT_POINT" \
  -o username="${SMB_USERNAME}",password="${SMB_PASSWORD}",vers=3.0

echo "[INFO] Mount ổ 1 thành công."

echo "[INFO] Đang mount //${SMB_SERVER_1}/${SMB_SHARE_1} -> ${MOUNT_POINT_1}..."
mount -t cifs "//${SMB_SERVER_1}/${SMB_SHARE_1}" "$MOUNT_POINT_1" \
  -o username="${SMB_USERNAME_1}",password="${SMB_PASSWORD_1}",vers=3.0

echo "[INFO] Mount ổ 2 thành công."
