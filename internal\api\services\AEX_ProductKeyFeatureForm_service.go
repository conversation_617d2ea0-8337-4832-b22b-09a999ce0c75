package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_ProductKeyFeatureForm struct {
	*BaseService
}

var AEX_ProductKeyFeatureForm_Service = &AEX_ProductKeyFeatureForm{}

func (s *AEX_ProductKeyFeatureForm) GetAllInlineSampleReviewMeeting() ([]types.AEXFProductKeyFeatureForm, error) {
	var arrProductKeyFeatureForm []types.AEXFProductKeyFeatureForm
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	query := `
	SELECT 
		CAST(No AS NVARCHAR(36)) AS No,
		PROJECTNAME,
		CATEGORY,
		USDEV,
		CLWID,
		TECHLVL,
		SEASON,
		DEVSTAGE,
		CTSDEV,
		DATE,
		FACTORY,
		CTSPM,
		UNC,
		BNA,
		COMPONENTS,
		LOGOS,
		OTHER,
		COMMENTS,
		UserID,
		UserDate,
		CreateDate,
		PDev,
		Key_Feature,
		UpperConstructionIMG,
		BottomAssemblyIMG,
		ComponentsIMG,
		LogosIMG,
		OtherIMG
	FROM AEX_Product_Key_Feature_Form
	ORDER BY CreateDate DESC
	`
	err = db.Raw(query).Scan(&arrProductKeyFeatureForm).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()

	return arrProductKeyFeatureForm, nil
}
func (s *AEX_ProductKeyFeatureForm) InsertAEX_ProductKeyFeatureForm(req *types.AEXFProductKeyFeatureForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}

	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var No string

	query := `
		INSERT INTO AEX_Product_Key_Feature_Form (
			PROJECTNAME,
			CATEGORY,
			USDEV,
			CLWID,
			TECHLVL,
			SEASON,
			DEVSTAGE,
			CTSDEV,
			DATE,
			FACTORY,
			CTSPM,
			UNC,
			BNA,
			COMPONENTS,
			LOGOS,
			OTHER,
			COMMENTS,
			UserID,
			UserDate,
			CreateDate,
			PDev,
			Key_Feature,
			UpperConstructionIMG,
			BottomAssemblyIMG,
			ComponentsIMG,
			LogosIMG,
			OtherIMG
		)
		OUTPUT CAST(INSERTED.No AS NVARCHAR(36)) AS No
		VALUES (
			?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), GETDATE(), ?, ?, ?, ?, ?, ?, ?
		)
	`
	if err := tx.Raw(
		query,
		req.PROJECTNAME,
		req.CATEGORY,
		req.USDEV,
		req.CLWID,
		req.TECHLVL,
		req.SEASON,
		req.DEVSTAGE,
		req.CTSDEV,
		req.DATE,
		req.FACTORY,
		req.CTSPM,
		req.UNC,
		req.BNA,
		req.COMPONENTS,
		req.LOGOS,
		req.OTHER,
		req.COMMENTS,
		req.UserID,
		req.PDev,
		req.Key_Feature,
		req.UpperConstructionIMG,
		req.BottomAssemblyIMG,
		req.ComponentsIMG,
		req.LogosIMG,
		req.OtherIMG,
	).Scan(&No).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return No, nil
}
func (s *AEX_ProductKeyFeatureForm) UpdateAEX_ProductKeyFeatureForm(req *types.AEXFProductKeyFeatureForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}

	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var No string

	query := `
		UPDATE AEX_Product_Key_Feature_Form
		SET
			PROJECTNAME = ?,
			CATEGORY = ?,
			USDEV = ?,
			CLWID = ?,
			TECHLVL = ?,
			SEASON = ?,
			DEVSTAGE = ?,
			CTSDEV = ?,
			DATE = ?,
			FACTORY = ?,
			CTSPM = ?,
			UNC = ?,
			BNA = ?,
			COMPONENTS = ?,
			LOGOS = ?,
			OTHER = ?,
			COMMENTS = ?,
			UserID = ?,
			UserDate = GETDATE(),
			PDev = ?,
			Key_Feature = ?,
			UpperConstructionIMG= ?,
			BottomAssemblyIMG= ?,
			ComponentsIMG= ?,
			LogosIMG= ?,
			OtherIMG= ?
		OUTPUT CAST(INSERTED.No AS NVARCHAR(36)) AS No
		WHERE No = ?
	`

	if err := tx.Raw(
		query,
		req.PROJECTNAME,
		req.CATEGORY,
		req.USDEV,
		req.CLWID,
		req.TECHLVL,
		req.SEASON,
		req.DEVSTAGE,
		req.CTSDEV,
		req.DATE,
		req.FACTORY,
		req.CTSPM,
		req.UNC,
		req.BNA,
		req.COMPONENTS,
		req.LOGOS,
		req.OTHER,
		req.COMMENTS,
		req.UserID,
		req.PDev,
		req.Key_Feature,
		req.UpperConstructionIMG,
		req.BottomAssemblyIMG,
		req.ComponentsIMG,
		req.LogosIMG,
		req.OtherIMG,
		req.No,
	).Scan(&No).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute update: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return No, nil
}
func (s *AEX_ProductKeyFeatureForm) DeleteAEX_ProductKeyFeatureForm(req *types.AEXFProductKeyFeatureForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var No string

	query := `
		DELETE FROM AEX_Product_Key_Feature_Form
		OUTPUT CAST(DELETED.No AS NVARCHAR(36)) AS No
		WHERE No = ?;
	`

	if err := tx.Raw(
		query,
		req.No,
	).Scan(
		&No,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return No, nil
}
