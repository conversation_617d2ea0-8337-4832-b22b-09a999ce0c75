stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  SERVICE_NAME: 'development-page-api'
  TAR_FILE: '${SERVICE_NAME}-bak'
  SERVER: ************

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker info
  script:
    - docker build -t ${SERVICE_NAME}:latest .
    - docker save -o ${SERVICE_NAME}.tar ${SERVICE_NAME}:latest
  artifacts:
    paths:
      - ${SERVICE_NAME}.tar
  only:
    - main

deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  dependencies:
    - build
  before_script:
    - apk add --update --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY_23_4" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H ${SERVER} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - ssh-add -l
  script:
    - ssh root@${SERVER} "mkdir -p /root/Scripts/${SERVICE_NAME}"
    - scp mount_smb.sh root@${SERVER}:/root/Scripts/${SERVICE_NAME}/mount_smb.sh
    - ssh root@${SERVER} "chmod +x /root/Scripts/${SERVICE_NAME}/mount_smb.sh"
    - scp ${SERVICE_NAME}.tar root@${SERVER}:/root/Build/${SERVICE_NAME}.tar
    - ssh root@${SERVER} "docker load -i /root/Build/${SERVICE_NAME}.tar"
    - ssh root@${SERVER} "docker compose -f /root/Docker/docker-compose.yaml config -q"
    - ssh root@${SERVER} "cd /root/Docker && docker compose up -d --no-build --pull=never ${SERVICE_NAME}"
  retry: 1
  only:
    - main