package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_ProductKeyFeatureForm(router *gin.RouterGroup) {
	router.POST("/GetAllProductKeyFeatureForm", controllers.AEX_ProductKeyFeatureForm.GetAllProductKeyFeatureForm)
	router.POST("/InsertAEX_ProductKeyFeatureForm", controllers.AEX_ProductKeyFeatureForm.InsertAEX_ProductKeyFeatureForm)
	router.PUT("/UpdateAEX_ProductKeyFeatureForm", controllers.AEX_ProductKeyFeatureForm.UpdateAEX_ProductKeyFeatureForm)
	router.DELETE("/DeleteAEX_ProductKeyFeatureForm", controllers.AEX_ProductKeyFeatureForm.DeleteAEX_ProductKeyFeatureForm)
}
