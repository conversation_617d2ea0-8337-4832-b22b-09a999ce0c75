package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_NPTFController struct {
	*BaseController
}

var AEX_NPTF = &AEX_NPTFController{}

func (c *AEX_NPTFController) GetAEX_NPTF(ctx *gin.Context) {
	result, err := services.AEX_NPTF.GetAEX_NPTF()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_NPTFController) InsertAEX_NPTF(ctx *gin.Context) {
	var requestParams *types.AEXNPTFForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_NPTF.InsertAEX_NPTF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`New Project Tracking`,`Insert`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}


func (c *AEX_NPTFController) UpdateAEX_NPTF(ctx *gin.Context) {
	var requestParams *types.AEXNPTFForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_NPTF.UpdateAEX_NPTF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`New Project Tracking`,`Update`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}

func (c *AEX_NPTFController) DeleteAEX_NPTF(ctx *gin.Context) {
	var requestParams *types.AEXNPTFForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_NPTF.DeleteAEX_NPTF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`New Project Tracking`,`Delete`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}