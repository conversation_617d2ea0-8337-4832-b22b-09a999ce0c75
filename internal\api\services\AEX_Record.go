package services

import (
	"database/sql"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_RecordServices struct {
	*BaseService
}

var AEX_Record = &AEX_RecordServices{}

func (s *AEX_RecordServices) InsertAEX_Record(Form string, Action string, UserID string) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO AEX_Record
		(
			ID,
			Form,
			Action,
			UserID,
			UserDate
		)
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		VALUES (NEWID(), ?, ?, ?, GETDATE());
	`
	if err := tx.Raw(
		query,
		Form,
		Action,
		UserID,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

func (s *AEX_RecordServices) GetAEX_Record(req *types.AEX_Search_Record) ([]types.AEX_Record, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var res []types.AEX_Record
	query := `
					SELECT 
						CAST(ID AS NVARCHAR(36)) AS ID,
						Form,
						Action,
						UserID,
						CONVERT(VARCHAR(19), UserDate, 120) as UserDate
					FROM AEX_Record
					WHERE 
						(@Form IS NULL OR Form LIKE '%' + @Form + '%') AND
						(@Action IS NULL OR Action LIKE '%' + @Action + '%') AND
						(@UserID IS NULL OR UserID LIKE '%' + @UserID + '%') AND
						(@StartDate IS NULL OR @StartDate = '' OR UserDate >= @StartDate) AND
						(@EndDate IS NULL OR @EndDate = '' OR UserDate < DATEADD(day, 1, @EndDate))
					ORDER BY UserDate DESC,Form
				`
	if err := tx.Raw(
		query,
		sql.Named("Form", req.Form),
		sql.Named("Action", req.Action),
		sql.Named("UserID", req.UserID),
		sql.Named("StartDate", req.StartDate),
		sql.Named("EndDate", req.EndDate),
	).Scan(
		&res,
	).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	return res, nil
}
