package types

type AEXNPTFForm struct {
	ID           string `json:"ID"`
	ProjectsName string `json:"ProjectsName"`
	CLWID        string `json:"CLWID"`
	SKU          string `json:"SKU"`
	TechLevel    string `json:"TechLevel"`
	CTSDEV       string `json:"CTSDEV"`
	LYNDEV       string `json:"LYNDEV"`
	CutDies      string `json:"CutDies"`
	ProdFactory  string `json:"ProdFactory"`
	Last         string `json:"Last"`
	Outsole      string `json:"Outsole"`
	Tooling      string `json:"Tooling"`
	Pullover     string `json:"Pullover"`
	SampleXF     string `json:"SampleXF"`
	UserID       string `json:"UserID"`
	UserDate     string `json:"UserDate"`
	CreateDate   string `json:"CreateDate"`
	Stage        string `json:"Stage"`
	Season       string `json:"Season"`
	Remark       string `json:"Remark"`
}
