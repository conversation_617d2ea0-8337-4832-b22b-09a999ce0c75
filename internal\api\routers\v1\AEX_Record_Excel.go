package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_Record_Excel_Router(router *gin.RouterGroup) {
    router.POST("/record-export-excel", controllers.AEX_Record_Excel.GetAEX_Record)
    router.POST("/record-export", controllers.AEX_Record_Excel.RecordExport)
    router.GET("/export-statistics", controllers.AEX_Record_Excel.GetExportStatistics)
    router.GET("/export-statistics/user/:userID", controllers.AEX_Record_Excel.GetUserExportStatistics)
    router.GET("/export-statistics/form/:form", controllers.AEX_Record_Excel.GetFormExportStatistics)
}