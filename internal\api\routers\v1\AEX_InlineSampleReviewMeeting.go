package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_InlineSampleReviewMeeting(router *gin.RouterGroup) {
	router.POST("/GetAllInlineSampleReviewMeeting", controllers.AEX_InlineSampleReviewMeeting.GetAllInlineSampleReviewMeeting)
	router.POST("/InsertAEXInlineSampleReviewMeeting", controllers.AEX_InlineSampleReviewMeeting.InsertAEX_InlineSampleReviewMeeting)
	router.PUT("/UpdateAEXInlineSampleReviewMeeting", controllers.AEX_InlineSampleReviewMeeting.UpdateAEX_InlineSampleReviewMeeting)
	router.DELETE("/DeleteAEXInlineSampleReviewMeeting", controllers.AEX_InlineSampleReviewMeeting.DeleteAEX_InlineSampleReviewMeeting)
}
