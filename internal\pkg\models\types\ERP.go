package types

type Image struct {
	Satge string `json:"Satge" form:"Satge"`
	Image string `json:"Image" form:"Image"`
}

type ModelColorway struct {
	DEVCODE  string `json:"DEVCODE"`
	ARTICLE  string `json:"ARTICLE"`
	XieMing  string `json:"XieMing"`
	Category string `json:"category"`
	Cut      string `json:"cut"`
	NA       string `json:"NA"`
	KFLX     string `json:"KFLX"`
	CTS      string `json:"CTS"`
	SS       string `json:"SS"`
	FD       string `json:"FD"`
	DDMH     string `json:"DDMH"`
	XTMH     string `json:"XTMH"`
	JiJie    string `json:"JiJie"`
}

type DEVCODE struct {
	DEVCODE string `json:"DEVCODE"` //ColorID
}

type XieMing struct {
	XieMing string `json:"XieMing"` //Modole Name
}

type JiJie struct {
	JiJie string `json:"Ji<PERSON><PERSON>"` // Season
}
type KFLX struct {
	KFLX string `json:"KFLX"` //TechLvl
}
type FD struct {
	FD string `json:"FD"` //FTYDev
}
type NA struct {
	NA string `json:"NA"` // HQDev
}
type CTS struct {
	CTS string `json:"CTS"` //  CLODev
}
type Category struct {
	Category string `json:"category"` //  CLODev
}
type Cut struct {
	Cut string `json:"cut"` //  CLODev
}
type SampleSize struct {
	SS string `json:"ss"`
}
type Season struct {
	Seasons string `json:"JiJie"`
}

type ARTICLE struct {
	ARTICLE string `json:"ARTICLE"`
}

type DDMH struct {
	DDMH string `json:"DDMH"`
}

type XTMH struct {
	XTMH string `json:"XTMH"`
}

type ARTICLE_DevCode struct {
	DEVCODE string `json:"DEVCODE"`
	ARTICLE string `json:"ARTICLE"`
}
