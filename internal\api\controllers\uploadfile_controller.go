package controllers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
	"web-api/internal/pkg/config"
	"web-api/internal/pkg/models/response"

	"math/rand"

	"github.com/gin-gonic/gin"
)

type UploadFileController struct {
	*BaseController
}

var UploadFile = &UploadFileController{}

var env = config.LoadFileENV()

func (c *UploadFileController) UploadFile(ctx *gin.Context) {
	file, err := ctx.FormFile("file")
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	now := time.Now()
	timestampNano := now.UnixNano()
	destDir := env.URL_DIR_WEB

	fileNameRender := fmt.Sprintf("%d_%s", timestampNano, file.Filename)
	destPath := filepath.Join(destDir, fileNameRender)

	src, err := file.Open()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	defer src.Close()

	dst, err := os.Create(destPath)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, fileNameRender)
}

func (c *UploadFileController) UploadFileFromFolder(ctx *gin.Context) {
	form, err := ctx.MultipartForm()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	files := form.File["file"]
	ModelName := ctx.PostForm("ModelName")
	ModelName = strings.ReplaceAll(ModelName, `/`, ``)
	if ModelName == "" {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Missing ModelName")
		return
	}

	now := time.Now()
	destDir := filepath.Join(env.URL_DIR_WEB, ModelName)
	fmt.Print(env.URL_DIR_WEB)
	if _, err := os.Stat(destDir); os.IsNotExist(err) {
		if err := os.MkdirAll(destDir, os.ModePerm); err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to create directory")
			return
		}
	}

	var uploadedFiles []string

	for _, file := range files {
		timestampNano := now.UnixNano()
		fileNameRender := fmt.Sprintf("%d_%s", timestampNano, file.Filename)
		destPath := filepath.Join(destDir, fileNameRender)

		src, err := file.Open()
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}
		defer src.Close()

		fileContent, err := io.ReadAll(src)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to read file content")
			return
		}

		minSize := 30 * 1024
		if len(fileContent) < minSize {
			paddingSize := minSize - len(fileContent)
			padding := make([]byte, paddingSize)
			for i := range padding {
				padding[i] = ' '
			}
			fileContent = append(fileContent, padding...)
		}

		dst, err := os.Create(destPath)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}
		defer dst.Close()

		if _, err := dst.Write(fileContent); err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to write file")
			return
		}

		uploadedFiles = append(uploadedFiles, fileNameRender)
	}

	response.OkWithData(ctx, uploadedFiles)
}

func (c *UploadFileController) DownloadFile(ctx *gin.Context) {
	var query struct {
		Filename string `form:"filename" binding:"required"`
	}
	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid 'filename' query parameter"})
		return
	}

	baseDir := env.URL_DIR_WEB

	filePath := filepath.Join(baseDir, query.Filename)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error accessing file"})
		return
	}

	ctx.FileAttachment(filePath, query.Filename)
}

func (c *UploadFileController) DownloadFileFromFolder(ctx *gin.Context) {
	var query struct {
		ModelName string `form:"ModelName" binding:"required"`
		Filename  string `form:"filename" binding:"required"`
	}
	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid parameters"})
		return
	}

	baseDir := env.URL_DIR_WEB
	ModelName := strings.ReplaceAll(query.ModelName, `/`, ``)

	destDir := filepath.Join(baseDir, ModelName)
	filePath := filepath.Join(destDir, query.Filename)

	cleanFilePath := filepath.Clean(filePath)

	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error resolving base directory"})
		return
	}

	absCleanFilePath, err := filepath.Abs(cleanFilePath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error resolving file path"})
		return
	}

	if !strings.HasPrefix(absCleanFilePath, absBaseDir) {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file path"})
		return
	}

	if _, err := os.Stat(cleanFilePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error accessing file"})
		return
	}

	ctx.FileAttachment(cleanFilePath, query.Filename)
}

func (c *UploadFileController) DeleteFile(ctx *gin.Context) {
	var query struct {
		Filename string `form:"filename" binding:"required"`
	}
	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid 'filename' query parameter"})
		return
	}

	baseDir := env.URL_DIR_WEB

	filePath := filepath.Join(baseDir, query.Filename)

	err := os.Remove(filePath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to delete file: %v", err)})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

func (c *UploadFileController) DeleteFileFromFolder(ctx *gin.Context) {
	var query struct {
		ModelName string `form:"ModelName" binding:"required"`
		Filename  string `form:"filename" binding:"required"`
	}
	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid parameters"})
		return
	}

	baseDir := env.URL_DIR_WEB
	ModelName := strings.ReplaceAll(query.ModelName, `/`, ``)
	destDir := filepath.Join(baseDir, ModelName)

	filePath := filepath.Join(destDir, query.Filename)

	cleanFilePath := filepath.Clean(filePath)
	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error resolving base directory"})
		return
	}

	absCleanFilePath, err := filepath.Abs(cleanFilePath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error resolving file path"})
		return
	}

	if !strings.HasPrefix(absCleanFilePath, absBaseDir+string(os.PathSeparator)) {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file path"})
		return
	}

	if _, err := os.Stat(cleanFilePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error accessing file"})
		return
	}

	if err := os.Remove(cleanFilePath); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to delete file: %v", err)})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

func (c *UploadFileController) DeleteModelDirectory(ctx *gin.Context) {
	var query struct {
		ModelName string `form:"ModelName" binding:"required"`
	}

	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid 'ModelName' parameter"})
		return
	}

	baseDir := env.URL_DIR_WEB
	ModelName := strings.ReplaceAll(query.ModelName, `/`, ``)
	dirPath := filepath.Join(baseDir, ModelName)

	cleanDirPath := filepath.Clean(dirPath)
	if !strings.HasPrefix(cleanDirPath, baseDir) {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid directory path"})
		return
	}

	if _, err := os.Stat(cleanDirPath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Directory not found"})
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error accessing directory"})
		return
	}

	if err := os.RemoveAll(cleanDirPath); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to delete directory: %v", err)})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Directory deleted successfully"})
}

func (c *UploadFileController) UploadMultiFileFromFolder(ctx *gin.Context) {
	form, err := ctx.MultipartForm()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	files := form.File["files"]
	ModelName := ctx.PostForm("ModelName")
	ModelName = strings.ReplaceAll(ModelName, `/`, ``)
	if ModelName == "" {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Missing ModelName")
		return
	}

	now := time.Now()
	destDir := filepath.Join(env.URL_DIR_WEB, ModelName)

	if _, err := os.Stat(destDir); os.IsNotExist(err) {
		if err := os.MkdirAll(destDir, os.ModePerm); err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to create directory")
			return
		}
	}

	var uploadedFiles []string

	for _, file := range files {
		timestampNano := now.UnixNano()
		fileNameRender := fmt.Sprintf("%d_%s", timestampNano, file.Filename)
		destPath := filepath.Join(destDir, fileNameRender)

		src, err := file.Open()
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}
		defer src.Close()

		fileContent, err := io.ReadAll(src)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to read file content")
			return
		}

		dst, err := os.Create(destPath)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}
		defer dst.Close()

		if _, err := dst.Write(fileContent); err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, "Failed to write file")
			return
		}

		uploadedFiles = append(uploadedFiles, fileNameRender)
	}

	response.OkWithData(ctx, uploadedFiles)
}


func (c *UploadFileController) LoadImageFromSpikeDevWebImage(ctx *gin.Context) {
	imageName := ctx.Query("name")
	if imageName == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing 'name' query parameter"})
		return
	}

	baseDir := filepath.Join(env.URL_DIR_ERP, "SPIKE", "DEV WEB IMAGE")
	imagePath := filepath.Join(baseDir, imageName)

	// Security: ensure the path is within the allowed directory
	cleanBaseDir, _ := filepath.Abs(baseDir)
	cleanImagePath, _ := filepath.Abs(imagePath)
	if !strings.HasPrefix(cleanImagePath, cleanBaseDir+string(os.PathSeparator)) && cleanImagePath != cleanBaseDir {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image path"})
		return
	}

	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
		return
	}

	ext := strings.ToLower(filepath.Ext(imageName))
	contentType := "application/octet-stream"
	switch ext {
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".png":
		contentType = "image/png"
	case ".gif":
		contentType = "image/gif"
	}

	ctx.Header("Content-Type", contentType)
	ctx.Header("Content-Disposition", "inline; filename=\""+imageName+"\"")
	ctx.File(imagePath)
}
func (c *UploadFileController) RandomTwoImageFilesFromSpikeDevWebImage(ctx *gin.Context) {
	baseDir := filepath.Join(env.URL_DIR_ERP, "SPIKE", "DEV WEB IMAGE")

	files, err := os.ReadDir(baseDir)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Cannot read directory"})
		return
	}

	var imageFiles []string
	seen := make(map[string]struct{})
	for _, file := range files {
		if !file.IsDir() {
			ext := strings.ToLower(filepath.Ext(file.Name()))
			if ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif" {
				if _, exists := seen[file.Name()]; !exists {
					imageFiles = append(imageFiles, file.Name())
					seen[file.Name()] = struct{}{}
				}
			}
		}
	}

	if len(imageFiles) < 2 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Not enough unique image files found"})
		return
	}

	randSeed := time.Now().UnixNano()
	r := rand.New(rand.NewSource(randSeed))
	r.Shuffle(len(imageFiles), func(i, j int) {
		imageFiles[i], imageFiles[j] = imageFiles[j], imageFiles[i]
	})

	ctx.JSON(http.StatusOK, gin.H{"images": []string{imageFiles[0], imageFiles[1]}})
}

func (c *UploadFileController) RandomOneVideoFileFromSpikeDevWebImage(ctx *gin.Context) {
	baseDir := filepath.Join(env.URL_DIR_ERP, "SPIKE", "DEV WEB CLIP")

	files, err := os.ReadDir(baseDir)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Cannot read directory"})
		return
	}

	var videoFiles []string
	seen := make(map[string]struct{})
	for _, file := range files {
		if !file.IsDir() {
			ext := strings.ToLower(filepath.Ext(file.Name()))
			if ext == ".mp4" || ext == ".avi" || ext == ".mov" || ext == ".wmv" || ext == ".mkv" {
				if _, exists := seen[file.Name()]; !exists {
					videoFiles = append(videoFiles, file.Name())
					seen[file.Name()] = struct{}{}
				}
			}
		}
	}

	if len(videoFiles) == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "No video files found"})
		return
	}

	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(videoFiles))
	
	randomVideo := videoFiles[randomIndex]
	
	videoPath := filepath.Join(baseDir, randomVideo)

	// Security: ensure the path is within the allowed directory
	cleanBaseDir, _ := filepath.Abs(baseDir)
	cleanVideoPath, _ := filepath.Abs(videoPath)
	if !strings.HasPrefix(cleanVideoPath, cleanBaseDir+string(os.PathSeparator)) && cleanVideoPath != cleanBaseDir {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video path"})
		return
	}

	ctx.Header("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate")
	ctx.Header("Pragma", "no-cache")
	ctx.Header("Expires", "0")
	ctx.Header("Surrogate-Control", "no-store")

	ctx.File(videoPath)
}


