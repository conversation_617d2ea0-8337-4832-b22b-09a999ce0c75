package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type ERPController struct {
	*BaseController
}

var ERP = &ERPController{}

func (c *ERPController) GetModelColorwayID(ctx *gin.Context) {
	var requestParams *types.DEVCODE
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.ERP.GetModelColorwayID(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *ERPController) GetCTS(ctx *gin.Context) {
	result, err := services.ERP.GetCTS()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetDEVCODE(ctx *gin.Context) {
	result, err := services.ERP.GetDEVCODE()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetFD(ctx *gin.Context) {
	result, err := services.ERP.GetFD()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetJiJie(ctx *gin.Context) {
	result, err := services.ERP.GetJiJie()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *ERPController) GetKFLX(ctx *gin.Context) {
	result, err := services.ERP.GetKFLX()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetNA(ctx *gin.Context) {
	result, err := services.ERP.GetNA()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *ERPController) GetXieMing(ctx *gin.Context) {
	result, err := services.ERP.GetXieMing()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *ERPController) GetCategory(ctx *gin.Context) {
	result, err := services.ERP.GetCategory()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *ERPController) GetCut(ctx *gin.Context) {
	result, err := services.ERP.GetCut()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *ERPController) GetSampleSize(ctx *gin.Context) {
	result, err := services.ERP.GetSampleSize()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *ERPController) GetSEASONMeetingForm(ctx *gin.Context) {
	result, err := services.ERP.GetSEASONMeetingForm()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetARTICLE(ctx *gin.Context) {
	result, err := services.ERP.GetARTICLE()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetFD2(ctx *gin.Context) {
	result, err := services.ERP.GetFD2()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetXTMH(ctx *gin.Context) {
	result, err := services.ERP.GetXTMH()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetDDMH(ctx *gin.Context) {
	result, err := services.ERP.GetDDMH()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}

func (c *ERPController) GetARTICLE_byDevCode(ctx *gin.Context) {
	result, err := services.ERP.GetARTICLE_byDevCode()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
