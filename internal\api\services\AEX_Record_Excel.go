package services

import (
	"context"
	"database/sql"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_RecordExcelServices struct {
	*BaseService
}

var AEX_RecordExcel = &AEX_RecordExcelServices{}

func (s *AEX_RecordExcelServices) RecordExport(ctx context.Context, userID, form string) error {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return fmt.Errorf("failed to connect database: %v", err)
	}
	
	dbInstance, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %v", err)
	}

	query := `
		MERGE [dbo].[AEX_Record_Excel] AS target
		USING (SELECT @p1 AS UserID, @p2 AS Form, CAST(GETDATE() AS DATE) AS ExportDate) AS source
		ON (target.UserID = source.UserID AND target.Form = source.Form AND target.ExportDate = source.ExportDate)
		WHEN MATCHED THEN
			UPDATE SET ExportCount = ExportCount + 1
		WHEN NOT MATCHED THEN
			INSERT (UserID, Form, ExportDate, ExportCount)
			VALUES (@p1, @p2, CAST(GETDATE() AS DATE), 1);
	`
	_, err = dbInstance.ExecContext(ctx, query, userID, form)
	return err
}

func (s *AEX_RecordExcelServices) GetExportStatistics(ctx context.Context) ([]types.ExportStatistics, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var statistics []types.ExportStatistics
	query := `
		SELECT 
			CAST(ID AS NVARCHAR(36)) AS ID,
			UserID,
			Form,
			CONVERT(VARCHAR(10), ExportDate, 120) as ExportDate,
			ExportCount
		FROM AEX_Record_Excel
		ORDER BY ExportDate DESC, Form
	`
	
	if err := db.Raw(query).Scan(&statistics).Error; err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	return statistics, nil
}

func (s *AEX_RecordExcelServices) GetUserExportStatistics(ctx context.Context, userID string) ([]types.ExportStatistics, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var statistics []types.ExportStatistics
	query := `
		SELECT 
			CAST(ID AS NVARCHAR(36)) AS ID,
			UserID,
			Form,
			CONVERT(VARCHAR(10), ExportDate, 120) as ExportDate,
			ExportCount
		FROM AEX_Record_Excel
		WHERE UserID = @p1
		ORDER BY ExportDate DESC, Form
	`
	
	if err := db.Raw(query, sql.Named("p1", userID)).Scan(&statistics).Error; err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	return statistics, nil
}

func (s *AEX_RecordExcelServices) GetFormExportStatistics(ctx context.Context, form string) ([]types.ExportStatistics, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	var statistics []types.ExportStatistics
	query := `
		SELECT 
			CAST(ID AS NVARCHAR(36)) AS ID,
			UserID,
			Form,
			CONVERT(VARCHAR(10), ExportDate, 120) as ExportDate,
			ExportCount
		FROM AEX_Record_Excel
		WHERE Form = @p1
		ORDER BY ExportDate DESC, UserID
	`
	
	if err := db.Raw(query, sql.Named("p1", form)).Scan(&statistics).Error; err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}

	return statistics, nil
}
