package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterAEX_FMEA_Router(router *gin.RouterGroup) {
	router.GET("/get-aex-fmea", controllers.AEX_FMEA.GetAEX_FMEA)
	router.POST("/aex-fmea", controllers.AEX_FMEA.InsertAEX_FMEA)
	router.PUT("/aex-fmea", controllers.AEX_FMEA.UpdateAEX_FMEA)
	router.DELETE("/aex-fmea", controllers.AEX_FMEA.DeleteAEX_FMEA)
	
}
