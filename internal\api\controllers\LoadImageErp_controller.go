package controllers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
)

func (c *ERPController) GetLinkImage(ctx *gin.Context) {
	var requestParams struct {
		SR string `form:"SR" `
	}
	if err := c.ValidateReqParams(ctx, &requestParams); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, err.Error())
		return
	}

	result, err := services.Common.GetImagebySRandStage(requestParams.SR)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *ERPController) LoadingImage(ctx *gin.Context) {
	var query struct {
		PathFile string `form:"PathFile"`
	}
	if err := ctx.ShouldBindQuery(&query); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Replace `/` to `\` để dễ tách path
	pathFile := strings.ReplaceAll(query.PathFile, "/", `\`)
	needle := `Develop Team\`

	start := strings.Index(pathFile, needle)
	if start == -1 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid path format"})
		fmt.Println("Không tìm thấy chuỗi 'Develop Team\\'")
		return
	}

	subPath := pathFile[start+len(needle):]

	// ✅ Chuẩn hóa dấu `/` lại cho Linux
	subPath = strings.ReplaceAll(subPath, `\`, `/`)
	fmt.Println("SubPath:", subPath)

	// ✅ Nối với base path mount
	basePath := env.URL_DIR_ERP // ví dụ: "/mnt/123_111"
	fullPath := filepath.Join(basePath, subPath)

	// ✅ Xử lý bỏ dấu "-" trước R1/R2/Lyn, với JPG hoặc JPEG
	re := regexp.MustCompile(`-(R\d+)-LYN\.(JPG|JPEG)$`)
	newPath := re.ReplaceAllString(fullPath, ` $1 LYN.$2`)

	cleanFilePath := filepath.Clean(newPath)

	if _, err := os.Stat(cleanFilePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		fmt.Println("File not found:", cleanFilePath)
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Error accessing file"})
		fmt.Println("Access error:", cleanFilePath)
		return
	}

	ctx.Header("Content-Type", "image/jpeg")
	ctx.Header("Content-Disposition", "inline")
	ctx.File(cleanFilePath)
}
