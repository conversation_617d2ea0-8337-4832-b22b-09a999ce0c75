package types

type AEX_PRAF struct {
	ID                      string `json:"ID" gorm:"column:ID"`
	MODELNAME               string `json:"MODELNAME" gorm:"column:MOD<PERSON><PERSON><PERSON>"`
	SEASON                  string `json:"SEASON" gorm:"column:SEASON"`
	CLWID                   string `json:"CLWID" gorm:"column:CLWID"`
	TECHLVL                 string `json:"TECHLVL" gorm:"column:TECHLVL"`
	HQDEV                   string `json:"HQDEV" gorm:"column:HQDEV"`
	CLODEV                  string `json:"CLODEV" gorm:"column:CLODEV"`
	FTYDEV                  string `json:"FTYDEV" gorm:"column:FTYDEV"`
	UserID                  string `json:"UserID" gorm:"column:UserID"`
	UserDate                string `json:"UserDate" gorm:"column:UserDate"`
	CreateDate              string `json:"CreateDate" gorm:"column:CreateDate"`
	Stage                   string `json:"Stage" gorm:"column:Stage"`
	ProductRickLevel        string `json:"ProductRickLevel" gorm:"column:ProductRickLevel"`
	FACTORY                 string `json:"FACTORY" gorm:"column:FACTORY"`
	DevelopmentCalendar     string `json:"DevelopmentCalendar" gorm:"column:DevelopmentCalendar"`
	DistributionType        string `json:"DistributionType" gorm:"column:DistributionType"`
	Collaboration           string `json:"Collaboration" gorm:"column:Collaboration"`
	WearTestStatus          string `json:"WearTestStatus" gorm:"column:WearTestStatus"`
	ProductForecast_Volumes string `json:"ProductForecast_Volumes" gorm:"column:ProductForecast_Volumes"`
	HQDevDirector           string `json:"HQDevDirector" gorm:"column:HQDevDirector"`
	ProductSafetyRisk       string `json:"ProductSafetyRisk" gorm:"column:ProductSafetyRisk"`
	MaterialFailed          string `json:"MaterialFailed" gorm:"column:MaterialFailed"`
	FinishedShoeFailed      string `json:"FinishedShoeFailed" gorm:"column:FinishedShoeFailed"`
	UpperMaterial           string `json:"UpperMaterial" gorm:"column:UpperMaterial"`
	LiningMaterial          string `json:"LiningMaterial" gorm:"column:LiningMaterial"`
	Component               string `json:"Component" gorm:"column:Component"`
	Process2nd              string `json:"Process2nd" gorm:"column:Process2nd"`
	FinishedShoe            string `json:"FinishedShoe" gorm:"column:FinishedShoe"`
	FactoryRiskLevel        string `json:"FactoryRiskLevel" gorm:"column:FactoryRiskLevel"`
	CTSRiskLevel            string `json:"CTSRiskLevel" gorm:"column:CTSRiskLevel"`
	HQRiskLevel             string `json:"HQRiskLevel" gorm:"column:HQRiskLevel"`
}

type MATERIAL_LIBRARY struct {
	MaterialID          string `json:"MaterialID" gorm:"column:MaterialID"`
	MaterialDescription string `json:"MaterialDescription" gorm:"column:MaterialDescription"`
	Vendor              string `json:"Vendor" gorm:"column:Vendor"`
}

type Material_ID struct {
	MaterialID string `json:"MaterialID" gorm:"column:MaterialID"`
	Vendor     string `json:"Vendor" gorm:"column:Vendor"`
}
