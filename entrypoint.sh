#!/bin/sh
set -e
set -a

if [ -f /app/.env.production ]; then
  . /app/.env.production
else
  echo "❌ Không tìm thấy /app/.env.production"
  exit 1
fi

set +a

echo "✅ Đã load biến môi trường, bắt đầu mount..."
/app/mount_smb.sh

SRC_DIR_1="/mnt/Development_Page"   #!CHANGE
SRC_DIR_2="/mnt/123_111"   #!CHANGE
DST_DIR="/app/data"

mkdir -p "$DST_DIR"

copy_from_mount() {
  local SRC_DIR=$1
  local NAME=$2

  if mountpoint -q "$SRC_DIR"; then
    echo "[INFO] SMB đã mount tại $SRC_DIR"
  else
    echo "[ERROR] Không mount được SMB tại $SRC_DIR"
  fi
}

copy_from_mount "$SRC_DIR_1" "mes"
copy_from_mount "$SRC_DIR_2" "ksc"

echo "✅ Đã mount xong, khởi động ứng dụng..."
exec /app/webapi
