package router_v1

import (
	"github.com/gin-gonic/gin"
)

func Register(router *gin.Engine) {
	v1 := router.Group("/api/v1")

	RegisterUploadFileRouter(v1.Group(""))

	RegisterCommonRouter(v1.Group(""))

	RegisterLoginRouter(v1.Group(""))

	RegisterAEX_PRAF_Router(v1.Group(""))
	RegisterAEX_InlineSampleReviewMeeting(v1.Group(""))
	RegisterERP(v1.Group(""))

	RegisterAEX_ProductKeyFeatureForm(v1.Group(""))

	RegisterAEX_FMEA_Router(v1.Group(""))

	RegisterAEX_NPTF_Router(v1.Group(""))
	RegisterAEX_Record_Router(v1.Group(""))
	
	RegisterAEX_Record_Excel_Router(v1.Group(""))

}
