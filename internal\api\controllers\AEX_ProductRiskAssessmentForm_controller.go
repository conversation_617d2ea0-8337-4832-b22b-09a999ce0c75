package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_PRAFController struct {
	*BaseController
}

var AEX_PRAF = &AEX_PRAFController{}

func (c *AEX_PRAFController) GetAEX_PRAF(ctx *gin.Context) {
	result, err := services.AEX_PRAF.GetAEX_PRAF()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_PRAFController) InsertAEX_PRAF(ctx *gin.Context) {
	var requestParams *types.AEX_PRAF
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_PRAF.InsertAEX_PRAF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Risk Assessment`,`Insert`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}


func (c *AEX_PRAFController) UpdateAEX_PRAF(ctx *gin.Context) {
	var requestParams *types.AEX_PRAF
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_PRAF.UpdateAEX_PRAF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Risk Assessment`,`Update`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}

func (c *AEX_PRAFController) DeleteAEX_PRAF(ctx *gin.Context) {
	var requestParams *types.AEX_PRAF
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_PRAF.DeleteAEX_PRAF(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Risk Assessment`,`Delete`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}


func (c *AEX_PRAFController) GetMaterialLibrary(ctx *gin.Context) {
	var requestParams *types.MATERIAL_LIBRARY
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_PRAF.GetMaterialLibrary(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}


func (c *AEX_PRAFController) GetALLMaterialLibrary(ctx *gin.Context) {
	result, err := services.AEX_PRAF.GetALLMaterialLibrary()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}