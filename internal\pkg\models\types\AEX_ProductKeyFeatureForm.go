package types

type AEXFProductKeyFeatureForm struct {
	No                   string `gorm:"column:No"`
	PROJECTNAME          string `gorm:"column:PROJECTNAME"`
	CATEGORY             string `gorm:"column:CATEGORY"`
	USDEV                string `gorm:"column:USDEV"`
	CLWID                string `gorm:"column:CLWID"`
	TECHLVL              string `gorm:"column:TECHLVL"`
	SEASON               string `gorm:"column:SEASON"`
	DEVSTAGE             string `gorm:"column:DEVS<PERSON><PERSON>"`
	CTSDEV               string `gorm:"column:CTSDEV"`
	DATE                 string `gorm:"column:DATE"`
	FACTORY              string `gorm:"column:FACTORY"`
	CTSPM                string `gorm:"column:CTSPM"`
	UNC                  string `gorm:"column:UNC"`
	BNA                  string `gorm:"column:BNA"`
	COMPONENTS           string `gorm:"column:COMPONENTS"`
	LOGOS                string `gorm:"column:LOGOS"`
	OTHER                string `gorm:"column:OTHER"`
	COMMENTS             string `gorm:"column:COMMENTS"`
	UserID               string `gorm:"column:UserID"`
	UserDate             string `gorm:"column:UserDate"`
	CreateDate           string `gorm:"column:CreateDate"`
	PDev                 string `gorm:"column:PDev"`
	Key_Feature          string `gorm:"column:Key_Feature"`
	UpperConstructionIMG string `gorm:"column:UpperConstructionIMG"`
	BottomAssemblyIMG    string `gorm:"column:BottomAssemblyIMG"`
	ComponentsIMG        string `gorm:"column:ComponentsIMG"`
	LogosIMG             string `gorm:"column:LogosIMG"`
	OtherIMG             string `gorm:"column:OtherIMG"`
}
