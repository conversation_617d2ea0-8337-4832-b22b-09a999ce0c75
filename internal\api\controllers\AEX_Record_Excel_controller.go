package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_Record_ExcelController struct {
	*BaseController
}

var AEX_Record_Excel = &AEX_Record_ExcelController{}

func (c *AEX_Record_ExcelController) GetAEX_Record(ctx *gin.Context) {
	var requestParams *types.AEX_Search_Record
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	result, err := services.AEX_Record.GetAEX_Record(requestParams)

	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_Record_ExcelController) RecordExport(ctx *gin.Context) {
	var requestParams *types.ExportRequest
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}

	err := services.AEX_RecordExcel.RecordExport(ctx, requestParams.UserID, requestParams.Form)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithMessage(ctx, "Export recorded successfully")
}

func (c *AEX_Record_ExcelController) GetExportStatistics(ctx *gin.Context) {
	result, err := services.AEX_RecordExcel.GetExportStatistics(ctx)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_Record_ExcelController) GetUserExportStatistics(ctx *gin.Context) {
	userID := ctx.Param("userID")
	result, err := services.AEX_RecordExcel.GetUserExportStatistics(ctx, userID)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_Record_ExcelController) GetFormExportStatistics(ctx *gin.Context) {
	form := ctx.Param("form")
	result, err := services.AEX_RecordExcel.GetFormExportStatistics(ctx, form)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}
