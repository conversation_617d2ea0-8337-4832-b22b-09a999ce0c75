package types

type AEXFMEAReportForm struct {
	ID                   string `json:"ID"`
	Stage                string `json:"Stage"`
	Skus                 string `json:"Skus"`
	Category             string `json:"Category"`
	Subcategory          string `json:"Subcategory"`
	IlusTfmode           string `json:"IlusTfmode"`
	FuncReq              string `json:"FuncReq"`
	PotentialFailureMode string `json:"PotentialFailureMode"`
	PotentialEffect      string `json:"PotentialEffect"`
	PotentialCause       string `json:"PotentialCause"`
	Prevention           string `json:"Prevention"`
	Detection            string `json:"Detection"`
	RecommendedAction    string `json:"RecommendedAction"`
	Responsibility       string `json:"Responsibility"`
	TargetCompletioDate  string `json:"TargetCompletioDate"`
	ActionsTaken         string `json:"ActionsTaken"`
	CLWID                string `json:"CLWID"`
	ProjectLead          string `json:"ProjectLead"`
	ProjectName          string `json:"ProjectName"`
	Season               string `json:"Season"`
	CoreTeam             string `json:"CoreTeam"`
	UserID               string `json:"UserID"`
	UserDate             string `json:"UserDate"`
	CreateDate           string `json:"CreateDate"`
}
