package controllers

import (
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
)

// FILE - ADMIN CONTROLLER
type AdminController struct {
	*BaseController
}

var Admin = &AdminController{}

// func LoginController
func (c *AdminController) Login(ctx *gin.Context) {
	var requestParams request.LoginRequest
	if err := c.ValidateReqParams(ctx, &requestParams); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, err.Error())
		return
	}

	token, err := services.Admin.LoginService(&requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusUnauthorized, nil, err.Error())
		return
	}

	response.OkWithData(ctx, token)
}

// func - UpdatePasswordController
func (c *AdminController) UpdatePassword(ctx *gin.Context) {
	var requestParams request.UpdatePasswordRequest
	if err := c.ValidateReqParams(ctx, &requestParams); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, err.Error())
		return
	}
	result, err := services.Admin.UpdatePasswordService(&requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
