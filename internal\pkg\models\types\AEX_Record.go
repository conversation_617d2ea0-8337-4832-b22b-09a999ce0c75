package types

type AEX_Record struct {
	ID       string `json:"ID" gorm:"column:ID"`
	Form     string `json:"Form" gorm:"column:Form"`
	Action   string `json:"Action" gorm:"column:Action"`
	UserID   string `json:"UserID" gorm:"column:UserID"`
	UserDate string `json:"UserDate" gorm:"column:UserDate"`
}

type AEX_Search_Record struct {
	Form      string `json:"Form" gorm:"column:Form"`
	Action    string `json:"Action" gorm:"column:Action"`
	UserID    string `json:"UserID" gorm:"column:UserID"`
	StartDate string `json:"StartDate" gorm:"column:StartDate"`
	EndDate   string `json:"EndDate" gorm:"column:EndDate"`
}
