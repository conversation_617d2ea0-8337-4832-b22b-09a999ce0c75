package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_ProductKeyFeatureFormController struct {
	*BaseController
}

var AEX_ProductKeyFeatureForm = &AEX_ProductKeyFeatureFormController{}

func (c *AEX_ProductKeyFeatureFormController) GetAllProductKeyFeatureForm(ctx *gin.Context) {
	result, err := services.AEX_ProductKeyFeatureForm_Service.GetAllInlineSampleReviewMeeting()
	if err != nil {
		log.Printf("Error fetching ProductKeyFeatureForm: %v", err)
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}
	response.OkWithData(ctx, result)
}
func (c *AEX_ProductKeyFeatureFormController) InsertAEX_ProductKeyFeatureForm(ctx *gin.Context) {
	var requestParams *types.AEXFProductKeyFeatureForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_ProductKeyFeatureForm_Service.InsertAEX_ProductKeyFeatureForm(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Key Feature`,`Insert`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}
func (c *AEX_ProductKeyFeatureFormController) UpdateAEX_ProductKeyFeatureForm(ctx *gin.Context) {
	var requestParams *types.AEXFProductKeyFeatureForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_ProductKeyFeatureForm_Service.UpdateAEX_ProductKeyFeatureForm(requestParams)
if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Key Feature`,`Update`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}

func (c *AEX_ProductKeyFeatureFormController) DeleteAEX_ProductKeyFeatureForm(ctx *gin.Context) {
	var requestParams *types.AEXFProductKeyFeatureForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_ProductKeyFeatureForm_Service.DeleteAEX_ProductKeyFeatureForm(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`Product Key Feature`,`Delete`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}
