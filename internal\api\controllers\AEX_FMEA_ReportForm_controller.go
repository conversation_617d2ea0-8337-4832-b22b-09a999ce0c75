package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type AEX_FMEAController struct {
	*BaseController
}

var AEX_FMEA = &AEX_FMEAController{}

func (c *AEX_FMEAController) GetAEX_FMEA(ctx *gin.Context) {
	result, err := services.AEX_FMEA.GetAEX_FMEA()
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

func (c *AEX_FMEAController) InsertAEX_FMEA(ctx *gin.Context) {
	var requestParams *types.AEXFMEAReportForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_FMEA.InsertAEX_FMEA(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`FMEA Report`,`Insert`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}


func (c *AEX_FMEAController) UpdateAEX_FMEA(ctx *gin.Context) {
	var requestParams *types.AEXFMEAReportForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_FMEA.UpdateAEX_FMEA(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`FMEA Report`,`Update`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}

func (c *AEX_FMEAController) DeleteAEX_FMEA(ctx *gin.Context) {
	var requestParams *types.AEXFMEAReportForm
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.AEX_FMEA.DeleteAEX_FMEA(requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}else{
		_, err := services.AEX_Record.InsertAEX_Record(`FMEA Report`,`Delete`,requestParams.UserID)
		if err != nil {
			response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
			return
		}

	}

	response.OkWithData(ctx, result)
}