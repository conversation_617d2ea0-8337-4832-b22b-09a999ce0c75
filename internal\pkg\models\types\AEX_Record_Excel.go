package types

type ExportRequest struct {
	Form   string `json:"form" binding:"required"`
	Action string `json:"action" binding:"required"`
	UserID string `json:"userId" binding:"required"`
}

type ExportStatistics struct {
	ID          string `json:"id" gorm:"column:ID"`
	UserID      string `json:"userId" gorm:"column:UserID"`
	Form        string `json:"form" gorm:"column:Form"`
	ExportDate  string `json:"exportDate" gorm:"column:ExportDate"`
	ExportCount int    `json:"exportCount" gorm:"column:ExportCount"`
}
