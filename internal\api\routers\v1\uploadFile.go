package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func RegisterUploadFileRouter(router *gin.RouterGroup) {
	router.POST("/uploadFile", controllers.UploadFile.UploadFile)
	router.POST("/uploadFileFromFolder", controllers.UploadFile.UploadFileFromFolder)
	router.GET("/downloadFile", controllers.UploadFile.DownloadFile)
	router.GET("/downloadFileFromFolder", controllers.UploadFile.DownloadFileFromFolder)
	router.DELETE("/deleteFile", controllers.UploadFile.DeleteFile)
	router.DELETE("/deleteFileFromFolder", controllers.UploadFile.DeleteFileFromFolder)
	router.DELETE("/deleteModelDirectory", controllers.UploadFile.DeleteModelDirectory)
	router.POST("/uploadMultiFileFromFolder", controllers.UploadFile.UploadMultiFileFromFolder)

	router.GET("/get-ramdom-name-file", controllers.UploadFile.RandomTwoImageFilesFromSpikeDevWebImage)
	router.GET("/get-loading-image", controllers.UploadFile.LoadImageFromSpikeDevWebImage)
	router.GET("/get-loading-video", controllers.UploadFile.RandomOneVideoFileFromSpikeDevWebImage)
}
