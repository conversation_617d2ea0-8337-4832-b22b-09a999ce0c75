# Binaries for programs and plugins
*.dll
*.so
*.dylib
# *.log

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/
bin/
tmp/
/Godeps/
.idea/
/uploads/
/upload/
# /log/

### VisualStudioCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.env.dev
