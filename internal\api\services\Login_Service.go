package services

import (
	"errors"
	"fmt"
	"time"

	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"

	"github.com/dgrijalva/jwt-go"
)

type LoginService struct {
	*BaseService
}

var LG = &LoginService{}

func (s *LoginService) Login_ERP(requestParams *types.Login_ERP) (types.Login_ERP, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return types.Login_ERP{}, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	query := `    
		SELECT USERID, USERNAME, PWD, Memo
		FROM Busers
		WHERE USERID = ? AND PWD = ?
	`

	var result types.Login_ERP
	err = db.Raw(query, requestParams.USERID, requestParams.PWD).Scan(&result).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return types.Login_ERP{}, err
	}

	if result.USERID == "" {
		err = errors.New("user not found")
		return types.Login_ERP{}, err
	}

	if result.Memo == "離職Nghi Viec" {
		err = errors.New("account is lock")
		return types.Login_ERP{}, err
	}

	tokenString, err := s.generateToken(result.USERID)
	if err != nil {
		return types.Login_ERP{}, err
	}

	result.Token = tokenString
	result.Role = "ERP"

	return result, nil
}

func (s *LoginService) generateToken(userId string) (string, error) {
	expirationTime := time.Now().Add(24 * time.Hour)

	// Tạo claims cho token
	claims := &jwt.StandardClaims{
		Subject:   userId,
		ExpiresAt: expirationTime.Unix(),
	}

	// Tạo token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("TYXUAN@123"))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
