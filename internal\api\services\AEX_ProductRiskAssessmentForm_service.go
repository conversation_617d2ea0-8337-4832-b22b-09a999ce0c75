package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_PRAFServices struct {
	*BaseService
}

var AEX_PRAF = &AEX_PRAFServices{}
func (s *AEX_PRAFServices) GetAEX_PRAF() ([]types.AEX_PRAF, error) {

	var res []types.AEX_PRAF
	db, err := database.DatabaseConnection_WEB()

	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()

	query := `
		SELECT	CAST(ID AS NVARCHAR(36)) AS ID,
				MODELNAME,
				SEASON,
				CLWID,
				TECHLVL,
				HQDEV,
				CLODEV,
				FTYDEV,
				UserID,
				UserDate,
				CreateDate,
				Stage,
				ProductRickLevel,
				FACTORY,
				DevelopmentCalendar,
				DistributionType,
				Collaboration,
				WearTestStatus,
				ProductForecast_Volumes,
				HQDevDirector,
				ProductSafetyRisk,
				MaterialFailed,
				FinishedShoeFailed,
				UpperMaterial,
				LiningMaterial,
				Component,
				Process2nd,
				FinishedShoe,
				FactoryRiskLevel,
				CTSRiskLevel,
				HQRiskLevel
		FROM AEX_Product_Risk_Assessment_Form
		ORDER BY CreateDate DESC
	`
	err = db.Raw(query).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()
	return res, nil
}

func (s *AEX_PRAFServices) InsertAEX_PRAF(req *types.AEX_PRAF) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO AEX_Product_Risk_Assessment_Form
		(
			ID,
			MODELNAME,
			SEASON,
			CLWID,
			TECHLVL,
			HQDEV,
			CLODEV,
			FTYDEV,
			UserID,
			UserDate,
			CreateDate,
			Stage,
			ProductRickLevel,
			FACTORY,
			DevelopmentCalendar,
			DistributionType,
			Collaboration,
			WearTestStatus,
			ProductForecast_Volumes,
			HQDevDirector,
			ProductSafetyRisk,
			MaterialFailed,
			FinishedShoeFailed,
			UpperMaterial,
			LiningMaterial,
			Component,
			Process2nd,
			FinishedShoe,
			FactoryRiskLevel,
			CTSRiskLevel,
			HQRiskLevel
		)
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		VALUES (NEWID(), ?, ?, ?, ?, ?, ?, ?, ?, GetDate(), GetDate(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?);
	`
	if err := tx.Raw(
		query,
		req.MODELNAME,
		req.SEASON,
		req.CLWID,
		req.TECHLVL,
		req.HQDEV,
		req.CLODEV,
		req.FTYDEV,
		req.UserID,
		req.Stage,
		req.ProductRickLevel,
		req.FACTORY,
		req.DevelopmentCalendar,
		req.DistributionType,
		req.Collaboration,
		req.WearTestStatus,
		req.ProductForecast_Volumes,
		req.HQDevDirector,
		req.ProductSafetyRisk,
		req.MaterialFailed,
		req.FinishedShoeFailed,
		req.UpperMaterial,
		req.LiningMaterial,
		req.Component,
		req.Process2nd,
		req.FinishedShoe,
		req.FactoryRiskLevel,
		req.CTSRiskLevel,
		req.HQRiskLevel,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

func (s *AEX_PRAFServices) UpdateAEX_PRAF(req *types.AEX_PRAF) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var ID string

	query := `
		UPDATE AEX_Product_Risk_Assessment_Form
		SET 
			MODELNAME = ?,
			SEASON = ?,
			CLWID = ?,
			TECHLVL = ?,
			HQDEV = ?,
			CLODEV = ?,
			FTYDEV = ?,
			UserID = ?,
			UserDate = GetDate(),
			Stage = ?,
			ProductRickLevel = ?,
			FACTORY = ?,
			DevelopmentCalendar = ?,
			DistributionType = ?,
			Collaboration = ?,
			WearTestStatus = ?,
			ProductForecast_Volumes = ?,
			HQDevDirector = ?,
			ProductSafetyRisk = ?,
			MaterialFailed = ?,
			FinishedShoeFailed = ?,
			UpperMaterial = ?,
			LiningMaterial = ?,
			Component = ?,
			Process2nd = ?,
			FinishedShoe = ?,
			FactoryRiskLevel =?,
			CTSRiskLevel = ?,
			HQRiskLevel = ?
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.MODELNAME,
		req.SEASON,
		req.CLWID,
		req.TECHLVL,
		req.HQDEV,
		req.CLODEV,
		req.FTYDEV,
		req.UserID,
		req.Stage,
		req.ProductRickLevel,
		req.FACTORY,
		req.DevelopmentCalendar,
		req.DistributionType,
		req.Collaboration,
		req.WearTestStatus,
		req.ProductForecast_Volumes,
		req.HQDevDirector,
		req.ProductSafetyRisk,
		req.MaterialFailed,
		req.FinishedShoeFailed,
		req.UpperMaterial,
		req.LiningMaterial,
		req.Component,
		req.Process2nd,
		req.FinishedShoe,
		req.FactoryRiskLevel,
		req.CTSRiskLevel,
		req.HQRiskLevel,
		req.ID,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

func (s *AEX_PRAFServices) DeleteAEX_PRAF(req *types.AEX_PRAF) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var updatedID string

	query := `
		DELETE FROM AEX_Product_Risk_Assessment_Form
		OUTPUT CAST(DELETED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.ID,
	).Scan(
		&updatedID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return updatedID, nil
}


func (s *AEX_PRAFServices) GetMaterialLibrary(param *types.MATERIAL_LIBRARY) (types.MATERIAL_LIBRARY, error) {
	var res types.MATERIAL_LIBRARY
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return res, err
	}
	dbInstance, _ := db.DB()

	query := `
		SELECT TOP 1 Material_ID AS MaterialID, MaterialName AS MaterialDescription, Supplier AS Vendor
		FROM MATERIAL_LIBRARY.dbo.MATERIAL
		WHERE Material_ID = ? and Supplier = ?
	`
	err = db.Raw(query,param.MaterialID,param.Vendor).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		dbInstance.Close()
		return res, err
	}
	dbInstance.Close()
	return res, nil
}

func (s *AEX_PRAFServices) GetALLMaterialLibrary() ( []types.Material_ID, error) {
	var res []types.Material_ID
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return res, err
	}
	dbInstance, _ := db.DB()

	query := `
		SELECT  Material_ID AS MaterialID, Supplier AS Vendor
		FROM MATERIAL_LIBRARY.dbo.MATERIAL
		GROUP BY Material_ID, Supplier
		ORDER BY Material_ID, Supplier
	`
	err = db.Raw(query).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		dbInstance.Close()
		return res, err
	}
	dbInstance.Close()
	return res, nil
}


