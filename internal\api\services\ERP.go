package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type ERPService struct {
	*BaseService
}

var ERP = &ERPService{}

func (s *CommonService) GetImagebySRandStage(SR string) ([]types.Image, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var res []types.Image
	query :=
		`
		WITH temp_data AS (
			SELECT DISTINCT TOP 1
			XXZLKF.imgnameR1,
			XXZLKF.imgnameR2,
			XXZLKF.imgnameR3,
			XXZLKF.imgnameR4,
			XXZLKF.imgnameCFM
			FROM XXZLKF
			LEFT JOIN KFXXZL ON KFXXZL.SheHao=XXZLKF.SheHao AND KFXXZL.XieXing=XXZLKF.XieXing
			LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao 
			WHERE DEVCODE!='' AND ypzl.GSDH ='CDC' 
			AND  DEVCODE = ?
		)
		SELECT 'R1' as Satge,temp_data.imgnameR1 as Image FROM temp_data
		UNION ALL
		SELECT 'R2',temp_data.imgnameR2 FROM temp_data
		UNION ALL
		SELECT 'R3',temp_data.imgnameR3 FROM temp_data
		UNION ALL
		SELECT 'R4',temp_data.imgnameR4 FROM temp_data
		UNION ALL
		SELECT 'PC',temp_data.imgnameCFM FROM temp_data
	`
	err = db.Raw(query, SR).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return res, nil
}

func (s *ERPService) GetModelColorwayID(param *types.DEVCODE) (types.ModelColorway, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return types.ModelColorway{}, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var colorwayIDLinkFields types.ModelColorway
	query :=
		`
		SELECT TOP 1 * FROM (
			SELECT DISTINCT
				KFXXZL.DEVCODE,
				KFXXZL.ARTICLE,
				KFXXZL.XieMing,
				XXZLKF.category,
				XXZLKF.cut,
				XXZLKF.NA,
				CASE KFXXZL.KFLX WHEN 'CU' THEN 'NC' WHEN 'MU' THEN 'NM' WHEN 'MU+' THEN 'NM+' ELSE KFXXZL.KFLX END AS KFLX,
				XXZLKF.CTS,
				XXZLKF.SS, 
				KFXXZL.FD,
				KFXXZL.DDMH,
				KFXXZL.XTMH,
				KFXXZL.JiJie 
			FROM XXZLKF
			LEFT JOIN KFXXZL ON KFXXZL.SheHao = XXZLKF.SheHao AND KFXXZL.XieXing = XXZLKF.XieXing
			LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao 
			WHERE DEVCODE = ? AND ypzl.GSDH = 'CDC'
		) AS sub
		ORDER BY DEVCODE DESC

	`
	err = db.Raw(query, param.DEVCODE).Scan(&colorwayIDLinkFields).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return types.ModelColorway{}, err
	}
	return colorwayIDLinkFields, nil
}

func (s *ERPService) GetDEVCODE() ([]types.DEVCODE, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var Devcode []types.DEVCODE

	query := `
		SELECT DISTINCT DEVCODE  FROM kfxxzl 
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE DEVCODE !='' AND ypzl.GSDH = 'CDC'
	`
	err = db.Raw(query).Scan(&Devcode).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return Devcode, nil
}

func (s *ERPService) GetXieMing() ([]types.XieMing, error) {
	var modelNames []types.XieMing
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	query := `
		SELECT DISTINCT XieMing  FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE XieMing !='' AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&modelNames).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return modelNames, nil
}

func (s *ERPService) GetNA() ([]types.NA, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var hqdev []types.NA

	query := `	
			SELECT DISTINCT XXZLKF.NA  FROM XXZLKF 
			LEFT JOIN ypzl ON YPZL.XieXing = XXZLKF.XieXing AND YPZL.SheHao = XXZLKF.SheHao
			WHERE NA != ''  AND ypzl.GSDH = 'CDC'
		`

	err = db.Raw(query).Scan(&hqdev).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return hqdev, nil
}

func (s *ERPService) GetKFLX() ([]types.KFLX, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var techLvl []types.KFLX
	query := `	SELECT DISTINCT 
					CASE kfxxzl.KFLX 
					WHEN 'CU' 
					THEN 'NC' 
					WHEN 'MU' 
					THEN 'NM' 
					WHEN 'MU+' 
					THEN 'NM+' 
					ELSE kfxxzl.KFLX 
					END  
					AS KFLX
				FROM kfxxzl
				WHERE kflx !=''`

	err = db.Raw(query).Scan(&techLvl).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return techLvl, nil
}

// func - GetSeasonERPService
func (s *ERPService) GetJiJie() ([]types.JiJie, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var season []types.JiJie

	query := `	SELECT DISTINCT KFXXZL.JiJie  FROM KFXXZL
				LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
				WHERE JiJie != '' AND ypzl.GSDH = 'CDC'`

	err = db.Raw(query).Scan(&season).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return season, nil
}

func (s *ERPService) GetCTS() ([]types.CTS, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var ctsdev []types.CTS
	query := `
		select DISTINCT CTS from XXZLKF
		LEFT JOIN ypzl ON YPZL.XieXing = XXZLKF.XieXing AND YPZL.SheHao = XXZLKF.SheHao
		WHERE CTS != '' AND ypzl.GSDH = 'CDC' 
	`
	err = db.Raw(query).Scan(&ctsdev).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return ctsdev, nil
}

func (s *ERPService) GetFD() ([]types.FD, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var fds []types.FD

	query := `
		SELECT DISTINCT KFXXZL.FD FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE KFXXZL.FD != '' AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&fds).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return fds, nil
}
func (s *ERPService) GetCategory() ([]types.Category, error) {

	// Kết nối cơ sở dữ liệu
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn lỗi dữ liệu Category
	var categories []types.Category

	query := "SELECT DISTINCT category FROM XXZLKF WHERE category != ''"

	err = db.Raw(query).Scan(&categories).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return categories, nil
}
func (s *ERPService) GetCut() ([]types.Cut, error) {

	// Kết nối cơ sở dữ liệu
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn lỗi dữ liệu Cut
	var cuts []types.Cut

	query := "SELECT DISTINCT cut FROM XXZLKF WHERE cut != ''"

	err = db.Raw(query).Scan(&cuts).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return cuts, nil
}
func (s *ERPService) GetSampleSize() ([]types.SampleSize, error) {

	// Kết nối cơ sở dữ liệu
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	// Truy vấn dữ liệu SS
	var SampleSize []types.SampleSize

	query := "SELECT DISTINCT ss FROM XXZLKF WHERE ss != ''"

	err = db.Raw(query).Scan(&SampleSize).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return SampleSize, nil
}
func (s *ERPService) GetSEASONMeetingForm() ([]types.Season, error) {

	// Kết nối cơ sở dữ liệu
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()

	var Seasons []types.Season

	query := `
    SELECT DISTINCT JiJie AS Seasons
    FROM KFXXZL 
    LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao 
    WHERE JiJie != '' AND ypzl.GSDH = 'CDC'
`

	err = db.Raw(query).Scan(&Seasons).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}

	return Seasons, nil
}

func (s *ERPService) GetARTICLE() ([]types.ARTICLE, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var ARTICLE []types.ARTICLE

	query := `
		SELECT DISTINCT KFXXZL.ARTICLE  FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE KFXXZL.ARTICLE != '' AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&ARTICLE).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return ARTICLE, nil
}

func (s *ERPService) GetFD2() ([]types.FD, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var FD []types.FD

	query := `
		SELECT DISTINCT KFXXZL.FD  FROM KFXXZL
		LEFT JOIN kfzl_GS ON kfzl_GS.KHBH=kfxxzl.KHDH
		WHERE kfzl_GS.GSBH='CDC'
	`

	err = db.Raw(query).Scan(&FD).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return FD, nil
}
func (s *ERPService) GetARTICLE_byDevCode() ([]types.ARTICLE_DevCode, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var ARTICLE_DevCode []types.ARTICLE_DevCode

	query := `
		SELECT 
		DISTINCT DEVCODE, KFXXZL.ARTICLE  
		FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE DEVCODE !=''AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&ARTICLE_DevCode).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return ARTICLE_DevCode, nil
}
func (s *ERPService) GetXTMH() ([]types.XTMH, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var XTMH []types.XTMH

	query := `
		SELECT DISTINCT XTMH  FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE DEVCODE !='' and XTMH != ''  AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&XTMH).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return XTMH, nil
}
func (s *ERPService) GetDDMH() ([]types.DDMH, error) {
	db, err := database.DatabaseConnection_ERP()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	defer dbInstance.Close()
	var DDMH []types.DDMH

	query := `
		SELECT DISTINCT DDMH  FROM KFXXZL
		LEFT JOIN ypzl ON YPZL.XieXing = KFXXZL.XieXing AND YPZL.SheHao = KFXXZL.SheHao
		WHERE DEVCODE !='' and DDMH != ''  AND ypzl.GSDH = 'CDC'
	`

	err = db.Raw(query).Scan(&DDMH).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return DDMH, nil
}
