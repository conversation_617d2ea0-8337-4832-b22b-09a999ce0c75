package config

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
)

type ENV struct {
	ENV           string
	HOST_WEB      string
	USER_NAME_WEB string
	PASSWORD_WEB  string
	DB_NAME_WEB   string
	URL_DIR_WEB   string
	URL_DIR_ERP   string
	HOST_ERP      string
	USER_NAME_ERP string
	PASSWORD_ERP  string
	DB_NAME_ERP   string
}

func LoadFileENV() *ENV {

	env := os.Getenv("ENV")
	if env == "" {
		env = "dev"
	}

	envFile := fmt.Sprintf(".env.%s", env)
	err := godotenv.Load(envFile)
	if err != nil {
		log.Fatalf("Error loading %s file", envFile)
	}

	return &ENV{
		ENV: os.Getenv("ENV"),
		//web
		HOST_WEB:      os.Getenv("HOST_WEB"),
		USER_NAME_WEB: os.Getenv("USER_NAME_WEB"),
		PASSWORD_WEB:  os.Getenv("PASSWORD_WEB"),
		DB_NAME_WEB:   os.Getenv("DB_NAME_WEB"),
		URL_DIR_WEB:   os.Getenv("URL_DIR_WEB"),
		//erp
		HOST_ERP:      os.Getenv("HOST_ERP"),
		USER_NAME_ERP: os.Getenv("USER_NAME_ERP"),
		PASSWORD_ERP:  os.Getenv("PASSWORD_ERP"),
		DB_NAME_ERP:   os.Getenv("DB_NAME_ERP"),
		URL_DIR_ERP:   os.Getenv("URL_DIR_ERP"),
	}
}
