package services

import (
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type AEX_NPTFServices struct {
	*BaseService
}

var AEX_NPTF = &AEX_NPTFServices{}

// SELECT
func (s *AEX_NPTFServices) GetAEX_NPTF() ([]types.AEXNPTFForm, error) {
	var res []types.AEXNPTFForm
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()

	query := `
		SELECT
			CAST(ID AS NVARCHAR(36)) AS ID,
			ProjectsName,
			CLWID,
			SKU,
			TechLevel,
			CTSDEV,
			LYNDEV,
			CutDies,
			ProdFactory,
			Last,
			Outsole,
			Tooling,
			CONVERT(Nvarchar(50), Pullover,111) as Pull<PERSON>,
			CONVERT(Nvarchar(50), SampleXF,111) as <PERSON>pleXF,
			UserID,
			UserDate,
			CreateDate,
			Stage,
			Season,
			Remark
		FROM AEX_NPTF_Form
		Order by CreateDate DESC
	`
	err = db.Raw(query).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()
	return res, nil
}
// INSERT
func (s *AEX_NPTFServices) InsertAEX_NPTF(req *types.AEXNPTFForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}
	var ID string
	query := `
		INSERT INTO AEX_NPTF_Form
		(
			ID,
			ProjectsName,
			CLWID,
			SKU,
			TechLevel,
			CTSDEV,
			LYNDEV,
			CutDies,
			ProdFactory,
			Last,
			Outsole,
			Tooling,
			Pullover,
			SampleXF,
			UserID,
			UserDate,
			CreateDate,
			Stage,
			Season,
			Remark
		)
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		VALUES (
			NEWID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GetDate(), GetDate(), ?, ?, ?
		);
	`
	if err := tx.Raw(
		query,
		req.ProjectsName,
		req.CLWID,
		req.SKU,
		req.TechLevel,
		req.CTSDEV,
		req.LYNDEV,
		req.CutDies,
		req.ProdFactory,
		req.Last,
		req.Outsole,
		req.Tooling,
		req.Pullover,
		req.SampleXF,
		req.UserID,
		req.Stage,
		req.Season,
		req.Remark,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

// UPDATE
func (s *AEX_NPTFServices) UpdateAEX_NPTF(req *types.AEXNPTFForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var ID string

	query := `
		UPDATE AEX_NPTF_Form
		SET 
			ProjectsName = ?,
			CLWID = ?,
			SKU = ?,
			TechLevel = ?,
			CTSDEV = ?,
			LYNDEV = ?,
			CutDies = ?,
			ProdFactory = ?,
			Last = ?,
			Outsole = ?,
			Tooling = ?,
			Pullover = ?,
			SampleXF = ?,
			UserID = ?,
			UserDate = GetDate(),
			Stage = ?,
			Season = ?,
			Remark = ?
		OUTPUT CAST(INSERTED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.ProjectsName,
		req.CLWID,
		req.SKU,
		req.TechLevel,
		req.CTSDEV,
		req.LYNDEV,
		req.CutDies,
		req.ProdFactory,
		req.Last,
		req.Outsole,
		req.Tooling,
		req.Pullover,
		req.SampleXF,
		req.UserID,
		req.Stage,
		req.Season,
		req.Remark,
		req.ID,
	).Scan(
		&ID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return ID, nil
}

// DELETE
func (s *AEX_NPTFServices) DeleteAEX_NPTF(req *types.AEXNPTFForm) (string, error) {
	db, err := database.DatabaseConnection_WEB()
	if err != nil {
		return "", fmt.Errorf("failed to connect database: %v", err)
	}
	tx := db.Begin()
	if tx.Error != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", tx.Error)
	}

	var deletedID string

	query := `
		DELETE FROM AEX_NPTF_Form
		OUTPUT CAST(DELETED.ID AS NVARCHAR(36)) AS ID
		WHERE ID = ?;
	`

	if err := tx.Raw(
		query,
		req.ID,
	).Scan(
		&deletedID,
	).Error; err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to execute query: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return deletedID, nil
}
